#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP服务部署验证脚本
解决多设备同步问题的验证工具
"""

import os
import json
import subprocess
from pathlib import Path

class MCPDeploymentChecker:
    """MCP部署检查器"""
    
    def __init__(self):
        self.issues = []
        self.local_mcp_path = Path("C:/tools/mcp_servers/prompt-server")
        self.cursor_config_path = Path.cwd().parent / ".cursor" / "mcp.json"
        
    def check_local_deployment(self):
        """检查本地部署"""
        print("🔍 检查本地MCP部署")
        print("-" * 40)
        
        # 检查目录结构
        if self.local_mcp_path.exists():
            print(f"✅ 本地MCP目录存在: {self.local_mcp_path}")
        else:
            print(f"❌ 本地MCP目录不存在: {self.local_mcp_path}")
            self.issues.append("本地MCP目录缺失")
            return False
        
        # 检查关键文件
        key_files = [
            "src/index.js",
            "package.json",
            "node_modules"
        ]
        
        for file_path in key_files:
            full_path = self.local_mcp_path / file_path
            if full_path.exists():
                print(f"✅ {file_path} 存在")
            else:
                print(f"❌ {file_path} 缺失")
                self.issues.append(f"关键文件缺失: {file_path}")
        
        return len(self.issues) == 0
    
    def check_node_environment(self):
        """检查Node.js环境"""
        print("\n🔍 检查Node.js环境")
        print("-" * 40)
        
        try:
            # 检查Node.js版本
            result = subprocess.run(['node', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✅ Node.js版本: {result.stdout.strip()}")
            else:
                print("❌ Node.js未安装或不可用")
                self.issues.append("Node.js环境问题")
                return False
        except Exception as e:
            print(f"❌ Node.js检查失败: {e}")
            self.issues.append("Node.js环境异常")
            return False
        
        try:
            # 检查npm版本
            result = subprocess.run(['npm', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✅ npm版本: {result.stdout.strip()}")
            else:
                print("❌ npm不可用")
                self.issues.append("npm环境问题")
        except Exception as e:
            print(f"❌ npm检查失败: {e}")
            self.issues.append("npm环境异常")
        
        return True
    
    def test_mcp_server(self):
        """测试MCP服务器"""
        print("\n🔍 测试MCP服务器")
        print("-" * 40)
        
        if not self.local_mcp_path.exists():
            print("❌ 本地MCP路径不存在，跳过测试")
            return False
        
        try:
            # 切换到MCP目录并测试
            os.chdir(self.local_mcp_path)
            
            # 测试服务器启动（快速测试）
            result = subprocess.run(['node', 'src/index.js', '--help'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 or "usage" in result.stdout.lower() or "help" in result.stdout.lower():
                print("✅ prompt-server 可以正常启动")
                return True
            else:
                print(f"❌ prompt-server 启动异常: {result.stderr}")
                self.issues.append("prompt-server启动失败")
                return False
                
        except subprocess.TimeoutExpired:
            print("⚠️ prompt-server 测试超时（可能正常）")
            return True
        except Exception as e:
            print(f"❌ prompt-server 测试失败: {e}")
            self.issues.append(f"prompt-server测试异常: {e}")
            return False
    
    def check_cursor_config(self):
        """检查Cursor配置"""
        print("\n🔍 检查Cursor配置")
        print("-" * 40)
        
        if not self.cursor_config_path.exists():
            print(f"❌ Cursor配置文件不存在: {self.cursor_config_path}")
            self.issues.append("Cursor配置文件缺失")
            return False
        
        try:
            with open(self.cursor_config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print(f"✅ Cursor配置文件存在: {self.cursor_config_path}")
            
            # 检查MCP服务器配置
            mcp_servers = config.get('mcpServers', {})
            
            expected_servers = ['prompt-server', 'byterover-memory', 'google-search']
            
            for server in expected_servers:
                if server in mcp_servers:
                    print(f"✅ {server} 配置存在")
                    
                    # 特别检查prompt-server的路径
                    if server == 'prompt-server':
                        args = mcp_servers[server].get('args', [])
                        if args and 'C:\\tools\\mcp_servers\\prompt-server' in args[0]:
                            print(f"✅ prompt-server 使用正确的本地路径")
                        else:
                            print(f"❌ prompt-server 路径配置错误: {args}")
                            self.issues.append("prompt-server路径配置错误")
                else:
                    print(f"❌ {server} 配置缺失")
                    self.issues.append(f"{server}配置缺失")
            
            return len([issue for issue in self.issues if 'prompt-server路径' in issue]) == 0
            
        except Exception as e:
            print(f"❌ Cursor配置检查失败: {e}")
            self.issues.append(f"Cursor配置检查异常: {e}")
            return False
    
    def check_byterover_mcp(self):
        """检查byterover-mcp"""
        print("\n🔍 检查byterover-mcp")
        print("-" * 40)
        
        try:
            result = subprocess.run(['npx', 'byterover-mcp', '--version'], 
                                  capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                print(f"✅ byterover-mcp 可用: {result.stdout.strip()}")
                return True
            else:
                print(f"❌ byterover-mcp 不可用: {result.stderr}")
                self.issues.append("byterover-mcp不可用")
                return False
        except subprocess.TimeoutExpired:
            print("⚠️ byterover-mcp 检查超时")
            return True
        except Exception as e:
            print(f"❌ byterover-mcp 检查失败: {e}")
            self.issues.append(f"byterover-mcp检查异常: {e}")
            return False
    
    def generate_report(self):
        """生成检查报告"""
        print("\n" + "=" * 50)
        print("📋 MCP部署检查报告")
        print("=" * 50)
        
        if not self.issues:
            print("🎉 所有检查通过！MCP服务已正确部署")
            print("\n✅ 部署状态:")
            print("   ✅ 本地MCP服务器: 正常")
            print("   ✅ Node.js环境: 正常")
            print("   ✅ Cursor配置: 正确")
            print("   ✅ byterover-mcp: 可用")
            
            print("\n💡 使用建议:")
            print("1. 重启Cursor以应用MCP配置")
            print("2. 在Cursor中测试MCP工具是否可用")
            print("3. 在另一台电脑上运行相同的部署脚本")
            
            return True
        else:
            print(f"⚠️ 发现 {len(self.issues)} 个问题需要解决:")
            for i, issue in enumerate(self.issues, 1):
                print(f"   {i}. {issue}")
            
            print("\n🔧 建议修复步骤:")
            print("1. 检查Node.js是否正确安装")
            print("2. 重新运行部署脚本")
            print("3. 验证网络连接（用于npm包下载）")
            
            return False
    
    def run_full_check(self):
        """运行完整检查"""
        print("🔬 MCP服务部署验证")
        print("=" * 50)
        
        checks = [
            self.check_local_deployment,
            self.check_node_environment,
            self.test_mcp_server,
            self.check_cursor_config,
            self.check_byterover_mcp
        ]
        
        for check in checks:
            check()
        
        return self.generate_report()

def main():
    """主函数"""
    checker = MCPDeploymentChecker()
    success = checker.run_full_check()
    
    if success:
        print("\n🚀 MCP服务已准备就绪！")
    else:
        print("\n🔧 请解决上述问题后重新检查")

if __name__ == "__main__":
    main()
