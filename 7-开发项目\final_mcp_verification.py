#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP多设备配置最终验证脚本
"""

import json
import subprocess
from pathlib import Path

def final_verification():
    print("🔬 MCP多设备配置最终验证")
    print("=" * 50)
    
    issues = []
    checks_passed = 0
    total_checks = 6
    
    # 检查1: 本地MCP服务器
    print("\n1️⃣ 检查本地MCP服务器")
    local_path = Path("C:/tools/mcp_servers/prompt-server")
    if local_path.exists() and (local_path / "src/index.js").exists():
        print("✅ 本地MCP服务器部署正确")
        checks_passed += 1
    else:
        print("❌ 本地MCP服务器缺失")
        issues.append("本地MCP服务器未正确部署")
    
    # 检查2: Cursor配置
    print("\n2️⃣ 检查Cursor配置")
    cursor_config_path = Path("../.cursor/mcp.json")
    if cursor_config_path.exists():
        try:
            with open(cursor_config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            servers = config.get('mcpServers', {})
            expected_servers = ['prompt-server', 'byterover-memory', 'google-search', 'time-mcp']
            
            if all(server in servers for server in expected_servers):
                print("✅ Cursor配置完整")
                
                # 检查prompt-server路径
                prompt_args = servers['prompt-server'].get('args', [])
                if prompt_args and 'C:\\tools\\mcp_servers\\prompt-server' in prompt_args[0]:
                    print("✅ prompt-server路径正确")
                    checks_passed += 1
                else:
                    print("❌ prompt-server路径错误")
                    issues.append("prompt-server路径配置错误")
            else:
                print("❌ Cursor配置不完整")
                issues.append("Cursor配置缺少必要的服务器")
        except Exception as e:
            print(f"❌ Cursor配置解析失败: {e}")
            issues.append("Cursor配置文件格式错误")
    else:
        print("❌ Cursor配置文件不存在")
        issues.append("Cursor配置文件缺失")
    
    # 检查3: 坚果云忽略规则
    print("\n3️⃣ 检查坚果云忽略规则")
    ignore_path = Path(".nutstore_ignore")
    if ignore_path.exists():
        print("✅ 坚果云忽略规则存在")
        checks_passed += 1
    else:
        print("❌ 坚果云忽略规则缺失")
        issues.append("坚果云忽略规则未创建")
    
    # 检查4: Node.js环境
    print("\n4️⃣ 检查Node.js环境")
    try:
        result = subprocess.run(['node', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Node.js可用: {result.stdout.strip()}")
            checks_passed += 1
        else:
            print("❌ Node.js不可用")
            issues.append("Node.js环境问题")
    except:
        print("❌ Node.js检查失败")
        issues.append("Node.js未安装或不可访问")
    
    # 检查5: npm包管理
    print("\n5️⃣ 检查npm包管理")
    try:
        result = subprocess.run(['npm', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ npm可用: {result.stdout.strip()}")
            checks_passed += 1
        else:
            print("❌ npm不可用")
            issues.append("npm环境问题")
    except:
        print("❌ npm检查失败")
        issues.append("npm未安装或不可访问")
    
    # 检查6: 文档和脚本
    print("\n6️⃣ 检查文档和脚本")
    required_files = [
        "MCP多设备使用指南_最终版.md",
        "mcp_health_check_new.py",
        ".nutstore_ignore"
    ]
    
    missing_files = []
    for file_name in required_files:
        if not Path(file_name).exists():
            missing_files.append(file_name)
    
    if not missing_files:
        print("✅ 所有文档和脚本完整")
        checks_passed += 1
    else:
        print(f"❌ 缺少文件: {missing_files}")
        issues.append(f"缺少必要文件: {missing_files}")
    
    # 生成最终报告
    print("\n" + "=" * 50)
    print("📋 最终验证报告")
    print("=" * 50)
    
    print(f"📊 检查结果: {checks_passed}/{total_checks} 通过")
    
    if issues:
        print(f"\n⚠️ 发现 {len(issues)} 个问题:")
        for i, issue in enumerate(issues, 1):
            print(f"   {i}. {issue}")
    else:
        print("\n✅ 未发现任何问题")
    
    # 最终评估
    if checks_passed == total_checks:
        print("\n🎉 MCP多设备配置完全成功！")
        print("\n✅ 配置状态:")
        print("   ✅ 本地MCP服务器: 已部署")
        print("   ✅ Cursor配置: 正确")
        print("   ✅ 坚果云同步: 已配置")
        print("   ✅ 环境依赖: 正常")
        print("   ✅ 文档脚本: 完整")
        
        print("\n🔄 下一步操作:")
        print("1. 完全关闭Cursor应用")
        print("2. 重新启动Cursor")
        print("3. 检查MCP工具是否在Cursor中可用")
        print("4. 在另一台电脑上运行相同的部署流程")
        
        print("\n📱 在另一台电脑上的操作:")
        print("1. 等待坚果云同步完成")
        print("2. 运行部署命令（见使用指南）")
        print("3. 重启Cursor")
        
        return True
    elif checks_passed >= 4:
        print("\n✅ MCP配置基本成功，但有小问题需要解决")
        print("\n🔧 建议:")
        print("1. 解决上述问题")
        print("2. 重新运行验证脚本")
        print("3. 完成后重启Cursor")
        
        return False
    else:
        print("\n❌ MCP配置存在重要问题")
        print("\n🔧 建议:")
        print("1. 检查Node.js和npm安装")
        print("2. 重新运行配置脚本")
        print("3. 解决所有问题后重新验证")
        
        return False

def main():
    """主函数"""
    success = final_verification()
    
    if success:
        print("\n🚀 MCP多设备同步配置完成！可以开始使用了。")
    else:
        print("\n🔧 请解决问题后重新验证。")

if __name__ == "__main__":
    main()
