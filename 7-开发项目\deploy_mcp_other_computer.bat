@echo off
chcp 65001 > nul
echo 🚀 MCP多设备部署脚本 - 另一台电脑使用
echo ================================================

echo.
echo 📁 创建本地MCP目录...
if not exist "C:\tools\mcp_servers\prompt-server" (
    mkdir "C:\tools\mcp_servers\prompt-server"
    echo ✅ 目录创建成功
) else (
    echo ✅ 目录已存在
)

echo.
echo 📋 复制MCP服务器文件...
xcopy "mcp-prompt-server\*" "C:\tools\mcp_servers\prompt-server\" /E /Y /Q
if %errorlevel% == 0 (
    echo ✅ 文件复制成功
) else (
    echo ❌ 文件复制失败
    pause
    exit /b 1
)

echo.
echo 📦 安装依赖包...
cd /d "C:\tools\mcp_servers\prompt-server"
call npm install
if %errorlevel% == 0 (
    echo ✅ 依赖安装成功
) else (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)

echo.
echo 🔍 运行健康检查...
cd /d "%~dp0"
python mcp_health_check_new.py

echo.
echo 🎉 MCP部署完成！
echo.
echo 📋 下一步操作：
echo 1. 完全关闭Cursor
echo 2. 重新启动Cursor  
echo 3. 检查MCP工具是否可用
echo.
pause
