@echo off
chcp 65001 >nul
echo ========================================
echo MCP服务简单检查
echo ========================================
echo.

echo [检查1] 检查本地目录是否存在...
if exist "C:\tools\mcp_servers\prompt-server" (
    echo ✅ C:\tools\mcp_servers\prompt-server 存在
) else (
    echo ❌ C:\tools\mcp_servers\prompt-server 不存在
    echo 需要手动创建此目录
)

echo.
echo [检查2] 检查源文件...
if exist "C:\tools\mcp_servers\prompt-server\src\index.js" (
    echo ✅ 主程序文件存在
) else (
    echo ❌ 主程序文件不存在
)

echo.
echo [检查3] 检查配置文件...
if exist ".cursor\mcp.json" (
    echo ✅ MCP配置文件存在
) else (
    echo ❌ MCP配置文件不存在
)

echo.
echo [检查4] 检查Node.js...
node --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ Node.js 已安装
    node --version
) else (
    echo ❌ Node.js 未安装或不在PATH中
)

echo.
echo [检查5] 检查npm...
npm --version >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ npm 已安装
    npm --version
) else (
    echo ❌ npm 未安装或不在PATH中
)

echo.
echo 检查完成！按任意键继续...
pause
