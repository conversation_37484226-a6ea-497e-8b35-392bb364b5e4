#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP服务健康检查脚本
"""

import subprocess
import sys
from pathlib import Path

def check_mcp_health():
    print("🔍 MCP服务健康检查")
    print("=" * 40)
    
    issues = 0
    
    # 检查本地服务器
    local_path = Path("C:/tools/mcp_servers/prompt-server")
    if local_path.exists():
        print("✅ 本地MCP服务器存在")
        
        # 检查关键文件
        key_files = ["src/index.js", "package.json"]
        for file_name in key_files:
            if (local_path / file_name).exists():
                print(f"✅ {file_name} 存在")
            else:
                print(f"❌ {file_name} 缺失")
                issues += 1
    else:
        print("❌ 本地MCP服务器缺失")
        issues += 1
    
    # 检查Cursor配置
    cursor_config = Path("../.cursor/mcp.json")
    if cursor_config.exists():
        print("✅ Cursor配置存在")
    else:
        print("❌ Cursor配置缺失")
        issues += 1
    
    # 检查byterover-mcp
    try:
        result = subprocess.run(['npx', 'byterover-mcp', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ byterover-mcp 正常")
        else:
            print("❌ byterover-mcp 异常")
            issues += 1
    except:
        print("❌ byterover-mcp 检查失败")
        issues += 1
    
    print(f"\n📊 健康检查结果: {issues} 个问题")
    
    if issues == 0:
        print("🎉 所有MCP服务正常！")
        return True
    else:
        print("⚠️ 发现问题，建议重新配置")
        return False

if __name__ == "__main__":
    check_mcp_health()
