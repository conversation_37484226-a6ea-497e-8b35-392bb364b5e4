@echo off
echo ========================================
echo 手动MCP部署脚本 - 简化版
echo ========================================
echo.

echo [1/4] 创建本地目录...
md "C:\tools" 2>nul
md "C:\tools\mcp_servers" 2>nul
md "C:\tools\mcp_servers\prompt-server" 2>nul
echo 目录创建完成

echo.
echo [2/4] 复制文件...
xcopy "%~dp0mcp-prompt-server\*" "C:\tools\mcp_servers\prompt-server\" /E /I /Y
echo 文件复制完成

echo.
echo [3/4] 安装依赖...
cd /d "C:\tools\mcp_servers\prompt-server"
npm install
echo 依赖安装完成

echo.
echo [4/4] 验证部署...
if exist "C:\tools\mcp_servers\prompt-server\src\index.js" (
    echo ✅ 部署成功！
) else (
    echo ❌ 部署失败
)

echo.
echo 部署完成！按任意键继续...
pause
