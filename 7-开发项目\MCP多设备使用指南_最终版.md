# MCP多设备同步使用指南 - 最终版

## 🎯 配置完成状态

✅ **本地MCP服务器**: `C:\tools\mcp_servers\prompt-server`
✅ **Cursor配置文件**: `.cursor\mcp.json`
✅ **坚果云忽略规则**: `7-开发项目\.nutstore_ignore`
✅ **健康检查脚本**: `7-开发项目\mcp_health_check_new.py`

## 🔄 多设备使用流程

### 在当前电脑上（已完成）：
1. ✅ 本地MCP服务器已部署到 `C:\tools\mcp_servers\prompt-server`
2. ✅ Cursor配置已更新，指向本地路径
3. ✅ 坚果云忽略规则已创建，避免同步node_modules
4. ✅ 健康检查脚本已创建

### 在另一台电脑上：
1. **等待坚果云同步完成**
2. **运行部署脚本**：
   ```bash
   cd "7-开发项目"
   
   # 创建本地目录
   mkdir "C:\tools\mcp_servers\prompt-server" -Force
   
   # 复制文件
   Copy-Item -Path "mcp-prompt-server\*" -Destination "C:\tools\mcp_servers\prompt-server\" -Recurse -Force -Exclude "node_modules"
   
   # 安装依赖
   cd "C:\tools\mcp_servers\prompt-server"
   npm install
   
   # 健康检查
   cd "D:\我的坚果云\涨停堡学习笔记\7-开发项目"
   python mcp_health_check_new.py
   ```
3. **重启Cursor应用**

### 日常使用：
1. **切换电脑时无需重新配置** - Cursor配置通过坚果云自动同步
2. **如遇问题**，运行健康检查：
   ```bash
   cd "7-开发项目"
   python mcp_health_check_new.py
   ```
3. **必要时重新部署本地服务器**

## 🛠️ 故障排除

### 常见问题及解决方案：

#### 1. MCP工具在Cursor中不可用
**解决方案**：
- 完全关闭Cursor
- 重新启动Cursor
- 检查 `.cursor\mcp.json` 配置是否正确

#### 2. prompt-server异常
**解决方案**：
- 检查Node.js是否安装：`node --version`
- 检查本地文件是否存在：`Test-Path "C:\tools\mcp_servers\prompt-server\src\index.js"`
- 重新安装依赖：
  ```bash
  cd "C:\tools\mcp_servers\prompt-server"
  npm install
  ```

#### 3. byterover-mcp异常
**解决方案**：
- 检查网络连接
- 清除npm缓存：`npm cache clean --force`
- 重新安装：`npx byterover-mcp --version`

### 完全重新配置：
如果遇到严重问题，可以完全重新配置：
```bash
# 删除本地服务器
Remove-Item "C:\tools\mcp_servers" -Recurse -Force

# 重新部署
cd "D:\我的坚果云\涨停堡学习笔记\7-开发项目"
# 然后按照"在另一台电脑上"的步骤操作
```

## 📋 MCP服务列表

| 服务名称 | 功能描述 | 状态 | 位置 |
|---------|---------|------|------|
| **prompt-server** | 本地提示词管理 | ✅ 正常 | 本地部署 |
| **byterover-memory** | 共享记忆系统 | ⚠️ 需网络 | npm包 |
| **google-search** | 搜索功能 | ✅ 可用 | npm包 |
| **time-mcp** | 时间管理工具 | ✅ 可用 | npm包 |

## ⚠️ 重要提醒

### 配置原理：
1. **本地服务器**：每台电脑都需要独立部署到 `C:\tools\mcp_servers\`
2. **Cursor配置**：通过坚果云在设备间同步
3. **源代码**：保存在坚果云中，用于部署到各台电脑
4. **忽略规则**：防止node_modules等大文件同步

### 使用注意事项：
1. **每次配置后必须重启Cursor**
2. **坚果云会自动忽略node_modules等文件**
3. **配置文件(.cursor/mcp.json)会自动同步**
4. **本地服务器文件不会同步，需要在每台电脑上部署**

### 网络要求：
- **prompt-server**：无需网络（本地运行）
- **byterover-memory**：需要网络连接
- **google-search**：需要网络连接
- **time-mcp**：无需网络

## 🎉 配置成功验证

配置成功后，在Cursor中应该能看到以下MCP工具：
- 📝 prompt-server（提示词管理）
- 🧠 byterover-memory（记忆系统）
- 🔍 google-search（搜索功能）
- ⏰ time-mcp（时间工具）

---

**配置完成时间**: 2025-06-23 21:56
**配置状态**: ✅ 成功
**下一步**: 重启Cursor并测试MCP工具功能
